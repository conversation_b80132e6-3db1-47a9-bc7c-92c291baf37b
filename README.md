# 网页游戏平台

一个简单的网页游戏平台，目前包含跳跳乐游戏，后续会添加更多游戏。

## 功能特点

- 🎮 游戏列表界面，可以选择不同的游戏
- 🦘 跳跳乐游戏：控制小球跳跃避开障碍物
- 📱 响应式设计，支持手机和电脑
- 🎯 得分系统和难度递增

## 如何运行

1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器：
   ```bash
   # 使用 Python 3
   python -m http.server 8000
   
   # 使用 Node.js (需要安装 http-server)
   npx http-server
   ```
3. 在浏览器中访问 `http://localhost:8000`

## 游戏说明

### 跳跳乐
- **目标**：控制红色小球跳跃，避开红色障碍物
- **操作**：
  - 电脑：按空格键跳跃
  - 手机：点击屏幕跳跃
- **得分**：成功避开一个障碍物得10分
- **难度**：每100分游戏速度会增加，障碍物生成间隔会缩短

## 文件结构

```
web_game/
├── index.html      # 主页面
├── styles.css      # 样式文件
├── script.js       # 游戏逻辑
└── README.md       # 说明文档
```

## 技术栈

- HTML5 Canvas
- CSS3 (Grid, Flexbox, 动画)
- 原生 JavaScript (ES6+)

## 后续计划

- [ ] 太空射击游戏
- [ ] 贪吃蛇游戏
- [ ] 俄罗斯方块游戏
- [ ] 本地存储最高分
- [ ] 音效和背景音乐
- [ ] 更多游戏关卡

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个游戏平台！
