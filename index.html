<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页游戏平台</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div class="container">
        <header>
            <h1>🎮 网页游戏平台</h1>
            <p>选择你喜欢的游戏开始游玩！</p>
        </header>

        <main class="game-list">
            <div class="game-card" data-game="jump-game">
                <div class="game-icon">🦘</div>
                <h3>跳跳乐大冒险</h3>
                <p>横版卷轴冒险！控制人形角色自动前进，跳跃躲避障碍，收集金币，击败敌人，到达终点！</p>
                <button class="play-btn">开始冒险</button>
            </div>

            <div class="game-card coming-soon">
                <div class="game-icon">🚀</div>
                <h3>太空射击</h3>
                <p>即将推出...</p>
                <button class="play-btn" disabled>敬请期待</button>
            </div>

            <div class="game-card coming-soon">
                <div class="game-icon">🐍</div>
                <h3>贪吃蛇</h3>
                <p>即将推出...</p>
                <button class="play-btn" disabled>敬请期待</button>
            </div>

            <div class="game-card" data-game="tetris-game">
                <div class="game-icon">🧩</div>
                <h3>俄罗斯方块</h3>
                <p>经典的俄罗斯方块游戏！旋转和移动方块，消除满行，挑战高分！</p>
                <button class="play-btn">开始游戏</button>
            </div>
        </main>
    </div>

    <!-- 游戏容器 -->
    <div id="game-container" class="game-container hidden">
        <div class="game-header">
            <button id="back-btn" class="back-btn">← 返回游戏列表</button>
            <h2 id="game-title">游戏</h2>
            <div class="score">得分: <span id="score">0</span></div>
        </div>
        <canvas id="game-canvas" width="800" height="600"></canvas>
        <div class="game-controls">
            <p id="game-instructions">🎮 游戏操作说明</p>
            <button id="restart-btn" class="restart-btn">重新开始</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>