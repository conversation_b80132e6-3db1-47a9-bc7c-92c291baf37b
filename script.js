// 游戏状态管理
let currentGame = null;
let gameRunning = false;
let jumpGameInstance = null;
let tetrisGameInstance = null;

// DOM 元素
const gameContainer = document.getElementById('game-container');
const canvas = document.getElementById('game-canvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const backBtn = document.getElementById('back-btn');
const restartBtn = document.getElementById('restart-btn');

// 游戏列表事件监听
document.addEventListener('DOMContentLoaded', function() {
    const gameCards = document.querySelectorAll('.game-card:not(.coming-soon)');
    
    gameCards.forEach(card => {
        card.addEventListener('click', function() {
            const gameType = this.dataset.game;
            startGame(gameType);
        });
    });
    
    // 返回按钮
    backBtn.addEventListener('click', returnToGameList);
    
    // 重新开始按钮
    restartBtn.addEventListener('click', function() {
        if (currentGame === 'jump-game') {
            initJumpGame();
        } else if (currentGame === 'tetris-game') {
            initTetrisGame();
        }
    });
});

// 启动游戏
function startGame(gameType) {
    currentGame = gameType;
    gameContainer.classList.remove('hidden');

    // 更新游戏标题和说明
    const gameTitle = document.getElementById('game-title');
    const gameInstructions = document.getElementById('game-instructions');

    if (gameType === 'jump-game') {
        gameTitle.textContent = '跳跳乐大冒险';
        gameInstructions.innerHTML = '🎮 空格跳跃 | A/D移动 | 收集金币💰 | 到达终点🏁';
        initJumpGame();
    } else if (gameType === 'tetris-game') {
        gameTitle.textContent = '俄罗斯方块';
        gameInstructions.innerHTML = '🎮 方向键移动/旋转 | 空格瞬间下降 | 消除满行得分 | 📱触摸滑动控制';
        initTetrisGame();
    }
}

// 返回游戏列表
function returnToGameList() {
    gameRunning = false;
    currentGame = null;
    jumpGameInstance = null;
    tetrisGameInstance = null;
    gameContainer.classList.add('hidden');
}

// 跳跳乐游戏 - 横版卷轴
class JumpGame {
    constructor() {
        this.groundY = 500;

        this.player = {
            x: 100, // 玩家在屏幕中固定位置
            y: this.groundY - 40,
            width: 20,
            height: 40,
            velocityX: 0,
            velocityY: 0,
            onGround: true,
            jumpCount: 0,
            maxJumps: 2,
            color: '#ff6b6b',
            direction: 1,
            animation: 0
        };

        // 世界坐标系统
        this.camera = {
            x: 0, // 摄像机位置
            speed: 1.5 // 减慢自动前进速度
        };

        this.world = {
            width: 5000, // 世界总宽度
            platforms: [], // 平台
            obstacles: [], // 障碍物
            collectibles: [], // 收集品
            enemies: [] // 敌人
        };

        this.particles = [];
        this.score = 0;
        this.distance = 0; // 前进距离
        this.gravity = 0.6;
        this.jumpPower = -12;
        this.moveSpeed = 4;
        this.airTime = 0;

        this.keys = {
            space: false,
            left: false,
            right: false
        };

        this.gameComplete = false;

        this.generateWorld();
        this.setupControls();
    }

    // 生成整个世界的关卡
    generateWorld() {
        // 清空世界
        this.world.platforms = [];
        this.world.obstacles = [];
        this.world.collectibles = [];
        this.world.enemies = [];

        // 生成地面平台
        for (let x = 0; x < this.world.width; x += 100) {
            this.world.platforms.push({
                x: x,
                y: this.groundY,
                width: 100,
                height: 100,
                type: 'ground'
            });
        }

        // 生成悬浮平台
        this.generatePlatforms();

        // 生成障碍物
        this.generateWorldObstacles();

        // 生成收集品
        this.generateCollectibles();

        // 生成敌人
        this.generateEnemies();

        // 生成终点
        this.world.finish = {
            x: this.world.width - 200,
            y: this.groundY - 100,
            width: 100,
            height: 100
        };
    }

    generatePlatforms() {
        // 减少平台密度，增加间距
        for (let x = 600; x < this.world.width - 600; x += 400 + Math.random() * 500) {
            const platformY = this.groundY - 120 - Math.random() * 150;
            const platformWidth = 100 + Math.random() * 100;

            this.world.platforms.push({
                x: x,
                y: platformY,
                width: platformWidth,
                height: 20,
                type: 'floating'
            });

            // 减少多层平台的概率
            if (Math.random() < 0.2) {
                this.world.platforms.push({
                    x: x + 80,
                    y: platformY - 100,
                    width: platformWidth - 40,
                    height: 20,
                    type: 'floating'
                });
            }
        }
    }

    generateWorldObstacles() {
        // 大幅减少障碍物密度，增加间距，前1000米为安全区
        for (let x = 1000; x < this.world.width - 500; x += 400 + Math.random() * 600) {
            const obstacleType = Math.random();

            if (obstacleType < 0.5) {
                // 地面尖刺 - 减少数量
                this.world.obstacles.push({
                    x: x,
                    y: this.groundY - 30,
                    width: 25,
                    height: 30,
                    type: 'spike',
                    color: '#e74c3c'
                });
            } else if (obstacleType < 0.8) {
                // 移动的锯子 - 减少移动范围和速度
                this.world.obstacles.push({
                    x: x,
                    y: this.groundY - 80,
                    width: 25,
                    height: 25,
                    type: 'saw',
                    color: '#8e44ad',
                    moveRange: 60,
                    moveSpeed: 1,
                    initialX: x
                });
            } else {
                // 火球发射器 - 增加射击间隔
                this.world.obstacles.push({
                    x: x,
                    y: this.groundY - 50,
                    width: 20,
                    height: 50,
                    type: 'cannon',
                    color: '#34495e',
                    lastShot: 0,
                    shootInterval: 4000 // 增加到4秒
                });
            }
        }
    }

    generateCollectibles() {
        // 减少收集品密度，但让它们更容易收集
        for (let x = 200; x < this.world.width; x += 150 + Math.random() * 200) {
            if (Math.random() < 0.6) {
                this.world.collectibles.push({
                    x: x,
                    y: this.groundY - 60 - Math.random() * 100, // 降低高度，更容易收集
                    width: 20, // 增大尺寸
                    height: 20,
                    type: 'coin',
                    color: '#f1c40f',
                    collected: false,
                    animation: 0
                });
            }
        }
    }

    generateEnemies() {
        // 大幅减少敌人数量，前1500米无敌人
        for (let x = 1500; x < this.world.width - 800; x += 800 + Math.random() * 1000) {
            this.world.enemies.push({
                x: x,
                y: this.groundY - 30,
                width: 25,
                height: 30,
                type: 'walker',
                color: '#e67e22',
                velocityX: -0.5 + Math.random() * 1, // 减慢移动速度
                patrolRange: 80,
                initialX: x
            });
        }
    }
    
    setupControls() {
        // 移除之前的事件监听器
        this.removeControls();

        // 键盘按下事件
        this.keydownHandler = (e) => {
            if (!gameRunning || jumpGameInstance !== this) return;

            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    this.keys.space = true;
                    this.jump();
                    break;
                case 'ArrowLeft':
                case 'KeyA':
                    e.preventDefault();
                    this.keys.left = true;
                    break;
                case 'ArrowRight':
                case 'KeyD':
                    e.preventDefault();
                    this.keys.right = true;
                    break;
            }
        };

        // 键盘释放事件
        this.keyupHandler = (e) => {
            if (!gameRunning || jumpGameInstance !== this) return;

            switch(e.code) {
                case 'Space':
                    this.keys.space = false;
                    break;
                case 'ArrowLeft':
                case 'KeyA':
                    this.keys.left = false;
                    break;
                case 'ArrowRight':
                case 'KeyD':
                    this.keys.right = false;
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);
        document.addEventListener('keyup', this.keyupHandler);

        // 触摸/点击控制
        this.clickHandler = () => {
            if (gameRunning && jumpGameInstance === this) {
                this.jump();
            }
        };
        canvas.addEventListener('click', this.clickHandler);

        this.touchHandler = (e) => {
            e.preventDefault();
            if (gameRunning && jumpGameInstance === this) {
                this.jump();
            }
        };
        canvas.addEventListener('touchstart', this.touchHandler);
    }

    removeControls() {
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }
        if (this.keyupHandler) {
            document.removeEventListener('keyup', this.keyupHandler);
        }
        if (this.clickHandler) {
            canvas.removeEventListener('click', this.clickHandler);
        }
        if (this.touchHandler) {
            canvas.removeEventListener('touchstart', this.touchHandler);
        }
    }
    
    jump() {
        // 二段跳机制
        if (this.player.jumpCount < this.player.maxJumps) {
            this.player.velocityY = this.jumpPower;
            this.player.jumpCount++;
            this.player.onGround = false;

            // 添加跳跃粒子效果
            this.addJumpParticles();
        }
    }

    // 滞空效果
    handleAirControl() {
        if (this.keys.space && !this.player.onGround && this.player.velocityY > -2) {
            this.player.velocityY -= 0.3; // 滞空效果
            this.airTime++;

            // 限制滞空时间
            if (this.airTime > 30) {
                this.airTime = 0;
            }
        }
    }

    // 添加跳跃粒子效果
    addJumpParticles() {
        for (let i = 0; i < 8; i++) {
            this.particles.push({
                x: this.player.x + this.player.width / 2,
                y: this.player.y + this.player.height,
                velocityX: (Math.random() - 0.5) * 6,
                velocityY: Math.random() * -3 - 1,
                life: 30,
                maxLife: 30,
                color: `hsl(${Math.random() * 60 + 30}, 70%, 60%)`
            });
        }
    }
    
    update() {
        if (this.gameComplete) return;

        // 自动前进摄像机 - 减慢速度
        this.camera.x += this.camera.speed;
        this.distance = this.camera.x;

        // 处理玩家水平移动（相对于摄像机）
        if (this.keys.left) {
            this.player.velocityX = -this.moveSpeed;
            this.player.direction = -1;
        } else if (this.keys.right) {
            this.player.velocityX = this.moveSpeed;
            this.player.direction = 1;
        } else {
            this.player.velocityX *= 0.8; // 摩擦力
        }

        // 处理滞空效果
        this.handleAirControl();

        // 更新玩家位置
        this.player.velocityY += this.gravity;
        this.player.x += this.player.velocityX;
        this.player.y += this.player.velocityY;

        // 玩家边界检测（不能离开屏幕）
        if (this.player.x < this.camera.x + 50) {
            this.player.x = this.camera.x + 50;
        }
        if (this.player.x > this.camera.x + canvas.width - 50) {
            this.player.x = this.camera.x + canvas.width - 50;
        }

        // 平台碰撞检测
        this.checkPlatformCollisions();

        // 更新动画
        this.player.animation += 0.2;

        // 更新世界对象
        this.updateWorldObjects();

        // 更新粒子效果
        this.updateParticles();

        // 碰撞检测
        this.checkCollisions();

        // 检查是否到达终点
        this.checkFinish();

        // 检查是否掉出世界
        if (this.player.y > this.groundY + 100) {
            this.gameOver();
        }
    }
    
    // 平台碰撞检测
    checkPlatformCollisions() {
        this.player.onGround = false;

        this.world.platforms.forEach(platform => {
            // 只检查摄像机范围内的平台
            if (platform.x + platform.width < this.camera.x - 100 ||
                platform.x > this.camera.x + canvas.width + 100) {
                return;
            }

            // 跳过地面平台的侧面碰撞检测
            if (platform.type === 'ground') {
                // 地面平台只检测顶部碰撞
                if (this.player.x + this.player.width > platform.x &&
                    this.player.x < platform.x + platform.width &&
                    this.player.y + this.player.height > platform.y &&
                    this.player.y + this.player.height < platform.y + platform.height + 10 &&
                    this.player.velocityY >= 0) {

                    this.player.y = platform.y - this.player.height;
                    this.player.velocityY = 0;
                    this.player.onGround = true;
                    this.player.jumpCount = 0;
                    this.airTime = 0;
                }
                return;
            }

            // 悬浮平台的完整碰撞检测
            const playerLeft = this.player.x;
            const playerRight = this.player.x + this.player.width;
            const playerTop = this.player.y;
            const playerBottom = this.player.y + this.player.height;

            const platformLeft = platform.x;
            const platformRight = platform.x + platform.width;
            const platformTop = platform.y;
            const platformBottom = platform.y + platform.height;

            // 检查是否有重叠
            if (playerRight > platformLeft && playerLeft < platformRight &&
                playerBottom > platformTop && playerTop < platformBottom) {

                // 计算重叠距离
                const overlapLeft = playerRight - platformLeft;
                const overlapRight = platformRight - playerLeft;
                const overlapTop = playerBottom - platformTop;
                const overlapBottom = platformBottom - playerTop;

                // 找到最小重叠方向
                const minOverlap = Math.min(overlapLeft, overlapRight, overlapTop, overlapBottom);

                if (minOverlap === overlapTop && this.player.velocityY >= 0) {
                    // 从上方落到平台上
                    this.player.y = platformTop - this.player.height;
                    this.player.velocityY = 0;
                    this.player.onGround = true;
                    this.player.jumpCount = 0;
                    this.airTime = 0;
                } else if (minOverlap === overlapBottom && this.player.velocityY < 0) {
                    // 从下方撞到平台
                    this.player.y = platformBottom;
                    this.player.velocityY = 0;
                } else if (minOverlap === overlapLeft && this.player.velocityX > 0) {
                    // 从左侧撞到平台
                    this.player.x = platformLeft - this.player.width;
                    this.player.velocityX = 0;
                } else if (minOverlap === overlapRight && this.player.velocityX < 0) {
                    // 从右侧撞到平台
                    this.player.x = platformRight;
                    this.player.velocityX = 0;
                }
            }
        });
    }

    // 更新世界对象
    updateWorldObjects() {
        const currentTime = Date.now();

        // 更新敌人
        this.world.enemies.forEach(enemy => {
            if (enemy.x + enemy.width < this.camera.x - 100 ||
                enemy.x > this.camera.x + canvas.width + 100) {
                return;
            }

            enemy.x += enemy.velocityX;

            // 巡逻逻辑
            if (Math.abs(enemy.x - enemy.initialX) > enemy.patrolRange) {
                enemy.velocityX *= -1;
            }
        });

        // 更新移动障碍物
        this.world.obstacles.forEach(obstacle => {
            if (obstacle.type === 'saw') {
                obstacle.x = obstacle.initialX + Math.sin(Date.now() * 0.002 * obstacle.moveSpeed) * obstacle.moveRange;
            } else if (obstacle.type === 'cannon') {
                if (currentTime - obstacle.lastShot > obstacle.shootInterval) {
                    // 发射火球 - 减慢速度
                    this.world.obstacles.push({
                        x: obstacle.x + obstacle.width,
                        y: obstacle.y + obstacle.height / 2,
                        width: 8,
                        height: 8,
                        type: 'fireball',
                        color: '#e74c3c',
                        velocityX: 1.5, // 减慢火球速度
                        velocityY: 0
                    });
                    obstacle.lastShot = currentTime;
                }
            } else if (obstacle.type === 'fireball') {
                obstacle.x += obstacle.velocityX;
                obstacle.y += obstacle.velocityY;
                obstacle.velocityY += 0.1; // 重力
            }
        });

        // 更新收集品动画
        this.world.collectibles.forEach(collectible => {
            if (!collectible.collected) {
                collectible.animation += 0.1;
                collectible.y += Math.sin(collectible.animation) * 0.5;
            }
        });

        // 移除屏幕外的火球
        this.world.obstacles = this.world.obstacles.filter(obstacle => {
            if (obstacle.type === 'fireball') {
                return obstacle.x > this.camera.x - 100 && obstacle.x < this.camera.x + canvas.width + 100;
            }
            return true;
        });
    }

    // 检查终点
    checkFinish() {
        const finish = this.world.finish;
        if (this.player.x + this.player.width > finish.x &&
            this.player.x < finish.x + finish.width &&
            this.player.y + this.player.height > finish.y &&
            this.player.y < finish.y + finish.height) {
            this.gameComplete = true;
            this.showVictoryScreen();
        }
    }

    // 更新粒子效果
    updateParticles() {
        this.particles.forEach((particle, index) => {
            particle.x += particle.velocityX;
            particle.y += particle.velocityY;
            particle.velocityY += 0.2; // 重力
            particle.life--;

            if (particle.life <= 0) {
                this.particles.splice(index, 1);
            }
        });
    }

    checkCollisions() {
        const margin = 2;

        // 检查障碍物碰撞
        this.world.obstacles.forEach(obstacle => {
            if (obstacle.x + obstacle.width < this.camera.x - 100 ||
                obstacle.x > this.camera.x + canvas.width + 100) {
                return;
            }

            if (this.player.x + margin < obstacle.x + obstacle.width &&
                this.player.x + this.player.width - margin > obstacle.x &&
                this.player.y + margin < obstacle.y + obstacle.height &&
                this.player.y + this.player.height - margin > obstacle.y) {
                this.gameOver();
            }
        });

        // 检查敌人碰撞
        this.world.enemies.forEach(enemy => {
            if (enemy.x + enemy.width < this.camera.x - 100 ||
                enemy.x > this.camera.x + canvas.width + 100) {
                return;
            }

            if (this.player.x + margin < enemy.x + enemy.width &&
                this.player.x + this.player.width - margin > enemy.x &&
                this.player.y + margin < enemy.y + enemy.height &&
                this.player.y + this.player.height - margin > enemy.y) {
                this.gameOver();
            }
        });

        // 检查收集品碰撞
        this.world.collectibles.forEach(collectible => {
            if (collectible.collected) return;

            if (collectible.x + collectible.width < this.camera.x - 100 ||
                collectible.x > this.camera.x + canvas.width + 100) {
                return;
            }

            if (this.player.x < collectible.x + collectible.width &&
                this.player.x + this.player.width > collectible.x &&
                this.player.y < collectible.y + collectible.height &&
                this.player.y + this.player.height > collectible.y) {
                collectible.collected = true;
                this.score += 50;
                scoreElement.textContent = this.score;

                // 添加收集特效
                this.addCollectParticles(collectible.x, collectible.y);
            }
        });
    }

    addCollectParticles(x, y) {
        for (let i = 0; i < 12; i++) {
            this.particles.push({
                x: x,
                y: y,
                velocityX: (Math.random() - 0.5) * 8,
                velocityY: Math.random() * -6 - 2,
                life: 40,
                maxLife: 40,
                color: `hsl(${50 + Math.random() * 20}, 80%, 60%)`
            });
        }
    }
    
    gameOver() {
        console.log('跳跳乐游戏结束被调用');
        gameRunning = false;

        // 显示游戏结束界面
        this.showGameOverScreen();
    }

    showGameOverScreen() {
        // 在画布上绘制游戏结束界面
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 游戏结束文字
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('游戏结束!', canvas.width / 2, canvas.height / 2 - 80);

        // 得分
        ctx.font = 'bold 32px Arial';
        ctx.fillText(`最终得分: ${this.score}`, canvas.width / 2, canvas.height / 2 - 20);

        // 重新开始提示
        ctx.font = '24px Arial';
        ctx.fillStyle = '#3498db';
        ctx.fillText('按 R 键或点击重新开始按钮重新游戏', canvas.width / 2, canvas.height / 2 + 40);

        // 返回菜单提示
        ctx.fillStyle = '#e74c3c';
        ctx.fillText('按 ESC 键返回游戏列表', canvas.width / 2, canvas.height / 2 + 80);

        ctx.textAlign = 'left'; // 重置文字对齐

        // 添加重新开始的键盘监听
        this.addGameOverControls();
    }

    addGameOverControls() {
        const gameOverHandler = (e) => {
            if (e.code === 'KeyR') {
                document.removeEventListener('keydown', gameOverHandler);
                initJumpGame();
            } else if (e.code === 'Escape') {
                document.removeEventListener('keydown', gameOverHandler);
                returnToGameList();
            }
        };

        document.addEventListener('keydown', gameOverHandler);
    }

    showVictoryScreen() {
        // 在画布上绘制胜利界面
        ctx.fillStyle = 'rgba(46, 204, 113, 0.9)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 胜利文字
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('🎉 恭喜通关! 🎉', canvas.width / 2, canvas.height / 2 - 80);

        // 得分和距离
        ctx.font = 'bold 32px Arial';
        ctx.fillText(`最终得分: ${this.score}`, canvas.width / 2, canvas.height / 2 - 20);
        ctx.fillText(`前进距离: ${Math.round(this.distance)}米`, canvas.width / 2, canvas.height / 2 + 20);

        // 重新开始提示
        ctx.font = '24px Arial';
        ctx.fillStyle = '#2c3e50';
        ctx.fillText('按 R 键重新挑战', canvas.width / 2, canvas.height / 2 + 80);

        ctx.textAlign = 'left'; // 重置文字对齐

        // 添加重新开始的键盘监听
        this.addGameOverControls();
    }
    
    draw() {
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 保存画布状态
        ctx.save();

        // 应用摄像机变换
        ctx.translate(-this.camera.x, 0);

        // 绘制背景
        this.drawBackground();

        // 绘制世界对象
        this.drawWorld();

        // 绘制粒子效果
        this.drawParticles();

        // 绘制玩家（人形）
        this.drawPlayer();

        // 恢复画布状态
        ctx.restore();

        // 绘制UI（不受摄像机影响）
        this.drawUI();
    }

    drawBackground() {
        // 绘制天空渐变
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(1, '#98D8E8');
        ctx.fillStyle = gradient;
        ctx.fillRect(this.camera.x, 0, canvas.width, canvas.height);

        // 绘制云朵（视差效果）
        this.drawClouds();

        // 绘制远山
        ctx.fillStyle = 'rgba(52, 73, 94, 0.3)';
        for (let i = 0; i < 5; i++) {
            const mountainX = this.camera.x * 0.1 + i * 200;
            ctx.beginPath();
            ctx.moveTo(mountainX, canvas.height);
            ctx.lineTo(mountainX + 100, canvas.height - 100);
            ctx.lineTo(mountainX + 200, canvas.height);
            ctx.fill();
        }
    }

    drawWorld() {
        // 绘制平台
        this.world.platforms.forEach(platform => {
            if (platform.x + platform.width < this.camera.x - 100 ||
                platform.x > this.camera.x + canvas.width + 100) {
                return;
            }

            if (platform.type === 'ground') {
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(platform.x, platform.y, platform.width, platform.height);
            } else {
                // 悬浮平台 - 更明显的设计
                ctx.fillStyle = '#3498db'; // 蓝色主体
                ctx.fillRect(platform.x, platform.y, platform.width, platform.height);

                // 顶部高光 - 表示可以站立
                ctx.fillStyle = '#5dade2';
                ctx.fillRect(platform.x, platform.y, platform.width, 3);

                // 底部阴影
                ctx.fillStyle = '#2980b9';
                ctx.fillRect(platform.x, platform.y + platform.height - 3, platform.width, 3);

                // 边框
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 2;
                ctx.strokeRect(platform.x, platform.y, platform.width, platform.height);

                // 平台标识 - 小圆点
                ctx.fillStyle = '#f1c40f';
                for (let i = 0; i < 3; i++) {
                    const dotX = platform.x + platform.width * 0.2 + i * platform.width * 0.3;
                    const dotY = platform.y + platform.height / 2;
                    ctx.beginPath();
                    ctx.arc(dotX, dotY, 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        });

        // 绘制障碍物
        this.drawWorldObstacles();

        // 绘制敌人
        this.drawEnemies();

        // 绘制收集品
        this.drawCollectibles();

        // 绘制终点
        this.drawFinish();
    }

    drawWorldObstacles() {
        this.world.obstacles.forEach(obstacle => {
            if (obstacle.x + obstacle.width < this.camera.x - 100 ||
                obstacle.x > this.camera.x + canvas.width + 100) {
                return;
            }

            ctx.fillStyle = obstacle.color;

            switch(obstacle.type) {
                case 'spike':
                    // 绘制尖刺
                    ctx.beginPath();
                    ctx.moveTo(obstacle.x, obstacle.y + obstacle.height);
                    ctx.lineTo(obstacle.x + obstacle.width/2, obstacle.y);
                    ctx.lineTo(obstacle.x + obstacle.width, obstacle.y + obstacle.height);
                    ctx.closePath();
                    ctx.fill();
                    break;
                case 'saw':
                    // 绘制旋转锯子
                    ctx.save();
                    ctx.translate(obstacle.x + obstacle.width/2, obstacle.y + obstacle.height/2);
                    ctx.rotate(Date.now() * 0.01);
                    ctx.beginPath();
                    for (let i = 0; i < 8; i++) {
                        const angle = (i / 8) * Math.PI * 2;
                        const radius = i % 2 === 0 ? 15 : 10;
                        const x = Math.cos(angle) * radius;
                        const y = Math.sin(angle) * radius;
                        if (i === 0) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }
                    ctx.closePath();
                    ctx.fill();
                    ctx.restore();
                    break;
                case 'cannon':
                    // 绘制大炮
                    ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(obstacle.x + obstacle.width, obstacle.y + obstacle.height/2 - 3, 15, 6);
                    break;
                case 'fireball':
                    // 绘制火球
                    ctx.beginPath();
                    ctx.arc(obstacle.x + obstacle.width/2, obstacle.y + obstacle.height/2,
                           obstacle.width/2, 0, Math.PI * 2);
                    ctx.fill();
                    // 火焰尾迹
                    ctx.fillStyle = '#f39c12';
                    ctx.beginPath();
                    ctx.arc(obstacle.x, obstacle.y + obstacle.height/2, 3, 0, Math.PI * 2);
                    ctx.fill();
                    break;
            }
        });
    }

    drawEnemies() {
        this.world.enemies.forEach(enemy => {
            if (enemy.x + enemy.width < this.camera.x - 100 ||
                enemy.x > this.camera.x + canvas.width + 100) {
                return;
            }

            ctx.fillStyle = enemy.color;

            // 简单的敌人形状
            ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);

            // 眼睛
            ctx.fillStyle = '#fff';
            ctx.fillRect(enemy.x + 5, enemy.y + 5, 4, 4);
            ctx.fillRect(enemy.x + 15, enemy.y + 5, 4, 4);

            // 嘴巴
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(enemy.x + 8, enemy.y + 15, 8, 3);
        });
    }

    drawCollectibles() {
        this.world.collectibles.forEach(collectible => {
            if (collectible.collected) return;

            if (collectible.x + collectible.width < this.camera.x - 100 ||
                collectible.x > this.camera.x + canvas.width + 100) {
                return;
            }

            ctx.fillStyle = collectible.color;

            // 绘制旋转的金币
            ctx.save();
            ctx.translate(collectible.x + collectible.width/2, collectible.y + collectible.height/2);
            ctx.rotate(collectible.animation);
            ctx.scale(1 + Math.sin(collectible.animation * 2) * 0.1, 1);

            ctx.beginPath();
            ctx.arc(0, 0, collectible.width/2, 0, Math.PI * 2);
            ctx.fill();

            // 金币上的符号
            ctx.fillStyle = '#e67e22';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('$', 0, 4);

            ctx.restore();
        });
    }

    drawFinish() {
        const finish = this.world.finish;
        if (finish.x + finish.width < this.camera.x - 100 ||
            finish.x > this.camera.x + canvas.width + 100) {
            return;
        }

        // 绘制终点旗帜
        ctx.fillStyle = '#e74c3c';
        ctx.fillRect(finish.x, finish.y, finish.width, finish.height);

        // 旗杆
        ctx.fillStyle = '#8e44ad';
        ctx.fillRect(finish.x + 10, finish.y, 5, finish.height + 50);

        // 旗帜
        ctx.fillStyle = '#f1c40f';
        ctx.beginPath();
        ctx.moveTo(finish.x + 15, finish.y + 10);
        ctx.lineTo(finish.x + 60, finish.y + 25);
        ctx.lineTo(finish.x + 15, finish.y + 40);
        ctx.closePath();
        ctx.fill();

        // "FINISH" 文字
        ctx.fillStyle = '#2c3e50';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('FINISH', finish.x + finish.width/2, finish.y + finish.height + 20);
    }

    // 绘制人形玩家
    drawPlayer() {
        const x = this.player.x;
        const y = this.player.y;
        const w = this.player.width;
        const h = this.player.height;

        ctx.fillStyle = this.player.color;

        // 头部
        ctx.beginPath();
        ctx.arc(x + w/2, y + 8, 6, 0, Math.PI * 2);
        ctx.fill();

        // 身体
        ctx.fillRect(x + w/2 - 3, y + 14, 6, 16);

        // 手臂 (有动画效果)
        const armOffset = Math.sin(this.player.animation) * 2;
        ctx.fillRect(x + w/2 - 8, y + 16 + armOffset, 4, 8);
        ctx.fillRect(x + w/2 + 4, y + 16 - armOffset, 4, 8);

        // 腿部 (跑步动画)
        const legOffset = this.player.onGround ? Math.sin(this.player.animation * 2) * 3 : 0;
        ctx.fillRect(x + w/2 - 4, y + 30, 3, 10 + legOffset);
        ctx.fillRect(x + w/2 + 1, y + 30, 3, 10 - legOffset);

        // 方向指示器
        if (this.player.direction === -1) {
            ctx.fillStyle = '#fff';
            ctx.fillRect(x + w/2 - 2, y + 6, 2, 2);
        } else {
            ctx.fillStyle = '#fff';
            ctx.fillRect(x + w/2, y + 6, 2, 2);
        }
    }

    // 绘制障碍物
    drawObstacles() {
        this.obstacles.forEach(obstacle => {
            ctx.fillStyle = obstacle.color;

            switch(obstacle.type) {
                case 'spike':
                    // 绘制尖刺
                    ctx.beginPath();
                    ctx.moveTo(obstacle.x, obstacle.y + obstacle.height);
                    ctx.lineTo(obstacle.x + obstacle.width/2, obstacle.y);
                    ctx.lineTo(obstacle.x + obstacle.width, obstacle.y + obstacle.height);
                    ctx.closePath();
                    ctx.fill();
                    break;
                case 'wall':
                    // 绘制墙壁
                    ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                    // 添加纹理
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 1;
                    for(let i = 0; i < obstacle.height; i += 10) {
                        ctx.beginPath();
                        ctx.moveTo(obstacle.x, obstacle.y + i);
                        ctx.lineTo(obstacle.x + obstacle.width, obstacle.y + i);
                        ctx.stroke();
                    }
                    break;
                case 'moving':
                    // 绘制移动障碍物
                    ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                    // 添加移动指示器
                    ctx.fillStyle = '#fff';
                    ctx.fillRect(obstacle.x + 5, obstacle.y + 5, 10, 2);
                    ctx.fillRect(obstacle.x + 5, obstacle.y + obstacle.height - 7, 10, 2);
                    break;
                case 'flying':
                    // 绘制飞行障碍物
                    ctx.beginPath();
                    ctx.ellipse(obstacle.x + obstacle.width/2, obstacle.y + obstacle.height/2,
                               obstacle.width/2, obstacle.height/2, 0, 0, Math.PI * 2);
                    ctx.fill();
                    break;
            }
        });
    }

    // 绘制粒子效果
    drawParticles() {
        this.particles.forEach(particle => {
            const alpha = particle.life / particle.maxLife;
            ctx.fillStyle = particle.color.replace(')', `, ${alpha})`).replace('hsl', 'hsla');
            ctx.fillRect(particle.x - 2, particle.y - 2, 4, 4);
        });
    }

    // 绘制UI
    drawUI() {
        // 绘制得分
        ctx.fillStyle = '#2c3e50';
        ctx.font = 'bold 20px Arial';
        ctx.fillText(`得分: ${this.score}`, 20, 40);

        // 绘制前进距离
        ctx.fillText(`距离: ${Math.round(this.distance)}m`, 20, 70);

        // 绘制进度条
        const progress = Math.min(this.distance / this.world.width, 1);
        const progressBarWidth = 200;
        const progressBarHeight = 10;
        const progressBarX = canvas.width - progressBarWidth - 20;
        const progressBarY = 20;

        // 进度条背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);

        // 进度条填充
        ctx.fillStyle = progress >= 1 ? '#2ecc71' : '#3498db';
        ctx.fillRect(progressBarX, progressBarY, progressBarWidth * progress, progressBarHeight);

        // 进度条边框
        ctx.strokeStyle = '#2c3e50';
        ctx.lineWidth = 2;
        ctx.strokeRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);

        // 进度文字
        ctx.fillStyle = '#2c3e50';
        ctx.font = '14px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(`${Math.round(progress * 100)}%`, progressBarX + progressBarWidth, progressBarY - 5);
        ctx.textAlign = 'left';

        // 绘制操作提示和安全区提示
        if (this.distance < 500) {
            ctx.fillStyle = 'rgba(44, 62, 80, 0.8)';
            ctx.font = '16px Arial';
            ctx.fillText('🎮 空格跳跃 | A/D移动 | 自动前进到终点!', 20, canvas.height - 60);

            ctx.fillStyle = 'rgba(52, 152, 219, 0.8)';
            ctx.fillText('🟦 蓝色平台可以站立和穿越！', 20, canvas.height - 40);

            if (this.distance < 1000) {
                ctx.fillStyle = 'rgba(46, 204, 113, 0.8)';
                ctx.fillText('🛡️ 安全区域 - 熟悉操作吧！', 20, canvas.height - 20);
            }
        } else if (this.distance < 1500) {
            ctx.fillStyle = 'rgba(230, 126, 34, 0.8)';
            ctx.font = '16px Arial';
            ctx.fillText('⚠️ 前方开始出现障碍物，小心！', 20, canvas.height - 20);
        }

        // 绘制跳跃次数指示器
        if (!this.player.onGround) {
            ctx.fillStyle = this.player.jumpCount >= this.player.maxJumps ? '#e74c3c' : '#3498db';
            for (let i = 0; i < this.player.maxJumps; i++) {
                if (i < this.player.jumpCount) {
                    ctx.fillRect(canvas.width - 60 + i * 15, 50, 10, 10);
                } else {
                    ctx.strokeStyle = ctx.fillStyle;
                    ctx.lineWidth = 2;
                    ctx.strokeRect(canvas.width - 60 + i * 15, 50, 10, 10);
                }
            }
        }

        // 如果游戏完成，显示胜利信息
        if (this.gameComplete) {
            this.showVictoryScreen();
        }
    }
    
    drawClouds() {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        
        // 简单的云朵效果
        const cloudPositions = [
            {x: 100, y: 100},
            {x: 300, y: 80},
            {x: 500, y: 120},
            {x: 700, y: 90}
        ];
        
        cloudPositions.forEach(cloud => {
            ctx.beginPath();
            ctx.arc(cloud.x, cloud.y, 20, 0, Math.PI * 2);
            ctx.arc(cloud.x + 20, cloud.y, 25, 0, Math.PI * 2);
            ctx.arc(cloud.x + 40, cloud.y, 20, 0, Math.PI * 2);
            ctx.fill();
        });
    }
    
    gameLoop() {
        if (gameRunning && jumpGameInstance === this) {
            this.update();
            this.draw();
            requestAnimationFrame(() => this.gameLoop());
        } else if (!gameRunning && jumpGameInstance === this) {
            // 游戏结束时仍然绘制结束画面
            this.draw();
            this.showGameOverScreen();
        }
    }
}

// 初始化跳跳乐游戏
function initJumpGame() {
    console.log('初始化跳跳乐游戏');
    gameRunning = true;
    scoreElement.textContent = '0';

    // 清理之前的游戏实例
    if (jumpGameInstance) {
        jumpGameInstance.removeControls();
    }

    jumpGameInstance = new JumpGame();
    console.log('跳跳乐实例创建完成，gameRunning:', gameRunning);
    jumpGameInstance.gameLoop();
}

// 俄罗斯方块游戏
class TetrisGame {
    constructor() {
        this.board = [];
        this.boardWidth = 10;
        this.boardHeight = 20;
        this.blockSize = 30;

        // 初始化游戏板
        this.initBoard();

        // 当前方块和下一个方块
        this.currentPiece = null;
        this.nextPiece = null;
        this.currentX = 0;
        this.currentY = 0;

        // 游戏状态
        this.score = 0;
        this.lines = 0;
        this.level = 1;
        this.dropTime = 0;
        this.dropInterval = 1000; // 1秒

        // 方块定义
        this.pieces = {
            I: {
                shape: [
                    [1, 1, 1, 1]
                ],
                color: '#00f5ff'
            },
            O: {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffff00'
            },
            T: {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1]
                ],
                color: '#800080'
            },
            S: {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0]
                ],
                color: '#00ff00'
            },
            Z: {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1]
                ],
                color: '#ff0000'
            },
            J: {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1]
                ],
                color: '#0000ff'
            },
            L: {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1]
                ],
                color: '#ffa500'
            }
        };

        this.pieceTypes = Object.keys(this.pieces);

        // 生成第一个方块
        this.spawnPiece();
        this.spawnNextPiece();

        this.setupControls();
    }

    initBoard() {
        this.board = [];
        for (let y = 0; y < this.boardHeight; y++) {
            this.board[y] = [];
            for (let x = 0; x < this.boardWidth; x++) {
                this.board[y][x] = 0;
            }
        }
    }

    spawnPiece() {
        if (this.nextPiece) {
            this.currentPiece = this.nextPiece;
        } else {
            const randomType = this.pieceTypes[Math.floor(Math.random() * this.pieceTypes.length)];
            this.currentPiece = {
                type: randomType,
                shape: JSON.parse(JSON.stringify(this.pieces[randomType].shape)),
                color: this.pieces[randomType].color
            };
        }

        this.currentX = Math.floor(this.boardWidth / 2) - Math.floor(this.currentPiece.shape[0].length / 2);
        this.currentY = 0;

        // 检查游戏结束
        if (this.checkCollision(this.currentPiece.shape, this.currentX, this.currentY)) {
            this.gameOver();
            return;
        }

        this.spawnNextPiece();
    }

    spawnNextPiece() {
        const randomType = this.pieceTypes[Math.floor(Math.random() * this.pieceTypes.length)];
        this.nextPiece = {
            type: randomType,
            shape: JSON.parse(JSON.stringify(this.pieces[randomType].shape)),
            color: this.pieces[randomType].color
        };
    }

    setupControls() {
        this.removeControls();

        this.keydownHandler = (e) => {
            if (!gameRunning || tetrisGameInstance !== this) return;

            switch(e.code) {
                case 'ArrowLeft':
                case 'KeyA':
                    e.preventDefault();
                    this.movePiece(-1, 0);
                    break;
                case 'ArrowRight':
                case 'KeyD':
                    e.preventDefault();
                    this.movePiece(1, 0);
                    break;
                case 'ArrowDown':
                case 'KeyS':
                    e.preventDefault();
                    this.movePiece(0, 1);
                    break;
                case 'ArrowUp':
                case 'KeyW':
                    e.preventDefault();
                    this.rotatePiece();
                    break;
                case 'Space':
                    e.preventDefault();
                    this.hardDrop();
                    break;
            }
        };

        document.addEventListener('keydown', this.keydownHandler);

        // 触摸控制
        this.touchStartX = 0;
        this.touchStartY = 0;

        this.touchStartHandler = (e) => {
            if (!gameRunning || tetrisGameInstance !== this) return;
            e.preventDefault();
            this.touchStartX = e.touches[0].clientX;
            this.touchStartY = e.touches[0].clientY;
        };

        this.touchEndHandler = (e) => {
            if (!gameRunning || tetrisGameInstance !== this) return;
            e.preventDefault();

            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;

            const deltaX = touchEndX - this.touchStartX;
            const deltaY = touchEndY - this.touchStartY;

            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                if (deltaX > 30) {
                    this.movePiece(1, 0);
                } else if (deltaX < -30) {
                    this.movePiece(-1, 0);
                }
            } else {
                if (deltaY > 30) {
                    this.movePiece(0, 1);
                } else if (deltaY < -30) {
                    this.hardDrop(); // 向上滑动快速下降
                }
            }
        };

        canvas.addEventListener('touchstart', this.touchStartHandler);
        canvas.addEventListener('touchend', this.touchEndHandler);

        // 点击旋转
        this.clickHandler = (e) => {
            if (!gameRunning || tetrisGameInstance !== this) return;
            e.preventDefault();
            this.rotatePiece();
        };

        canvas.addEventListener('click', this.clickHandler);
    }

    removeControls() {
        if (this.keydownHandler) {
            document.removeEventListener('keydown', this.keydownHandler);
        }
        if (this.touchStartHandler) {
            canvas.removeEventListener('touchstart', this.touchStartHandler);
        }
        if (this.touchEndHandler) {
            canvas.removeEventListener('touchend', this.touchEndHandler);
        }
        if (this.clickHandler) {
            canvas.removeEventListener('click', this.clickHandler);
        }
    }

    movePiece(dx, dy) {
        const newX = this.currentX + dx;
        const newY = this.currentY + dy;

        if (!this.checkCollision(this.currentPiece.shape, newX, newY)) {
            this.currentX = newX;
            this.currentY = newY;
            return true;
        }
        return false;
    }

    rotatePiece() {
        const rotated = this.rotateMatrix(this.currentPiece.shape);
        if (!this.checkCollision(rotated, this.currentX, this.currentY)) {
            this.currentPiece.shape = rotated;
        }
    }

    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = [];

        for (let i = 0; i < cols; i++) {
            rotated[i] = [];
            for (let j = 0; j < rows; j++) {
                rotated[i][j] = matrix[rows - 1 - j][i];
            }
        }

        return rotated;
    }

    checkCollision(shape, x, y) {
        for (let py = 0; py < shape.length; py++) {
            for (let px = 0; px < shape[py].length; px++) {
                if (shape[py][px]) {
                    const newX = x + px;
                    const newY = y + py;

                    if (newX < 0 || newX >= this.boardWidth ||
                        newY >= this.boardHeight ||
                        (newY >= 0 && this.board[newY][newX])) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    hardDrop() {
        // 快速下降到底部
        while (!this.checkCollision(this.currentPiece.shape, this.currentX, this.currentY + 1)) {
            this.currentY++;
        }
        // 立即放置方块
        this.placePiece();
        this.clearLines();
        this.spawnPiece();
        this.dropTime = 0; // 重置下降时间
    }

    update(deltaTime) {
        if (!gameRunning) return;

        this.dropTime += deltaTime;

        if (this.dropTime >= this.dropInterval) {
            if (!this.movePiece(0, 1)) {
                // 方块无法继续下落，固定到游戏板
                this.placePiece();
                this.clearLines();
                this.spawnPiece();
            }
            this.dropTime = 0;
        }
    }

    placePiece() {
        for (let py = 0; py < this.currentPiece.shape.length; py++) {
            for (let px = 0; px < this.currentPiece.shape[py].length; px++) {
                if (this.currentPiece.shape[py][px]) {
                    const x = this.currentX + px;
                    const y = this.currentY + py;
                    if (y >= 0) {
                        this.board[y][x] = this.currentPiece.color;
                    }
                }
            }
        }
    }

    clearLines() {
        let linesCleared = 0;

        for (let y = this.boardHeight - 1; y >= 0; y--) {
            let fullLine = true;
            for (let x = 0; x < this.boardWidth; x++) {
                if (!this.board[y][x]) {
                    fullLine = false;
                    break;
                }
            }

            if (fullLine) {
                // 移除满行
                this.board.splice(y, 1);
                // 在顶部添加新的空行
                this.board.unshift(new Array(this.boardWidth).fill(0));
                linesCleared++;
                y++; // 重新检查当前行
            }
        }

        if (linesCleared > 0) {
            this.lines += linesCleared;

            // 计分系统
            const points = [0, 100, 300, 500, 800];
            this.score += points[linesCleared] * this.level;

            // 升级系统
            this.level = Math.floor(this.lines / 10) + 1;
            this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 100);

            scoreElement.textContent = this.score;
        }
    }

    gameOver() {
        gameRunning = false;
        this.showGameOverScreen();
    }

    showGameOverScreen() {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = '#fff';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('游戏结束!', canvas.width / 2, canvas.height / 2 - 80);

        ctx.font = 'bold 32px Arial';
        ctx.fillText(`最终得分: ${this.score}`, canvas.width / 2, canvas.height / 2 - 20);
        ctx.fillText(`消除行数: ${this.lines}`, canvas.width / 2, canvas.height / 2 + 20);
        ctx.fillText(`等级: ${this.level}`, canvas.width / 2, canvas.height / 2 + 60);

        ctx.font = '24px Arial';
        ctx.fillStyle = '#3498db';
        ctx.fillText('按 R 键重新开始', canvas.width / 2, canvas.height / 2 + 120);

        ctx.fillStyle = '#e74c3c';
        ctx.fillText('按 ESC 键返回菜单', canvas.width / 2, canvas.height / 2 + 160);

        ctx.textAlign = 'left';

        this.addGameOverControls();
    }

    addGameOverControls() {
        const gameOverHandler = (e) => {
            if (e.code === 'KeyR') {
                document.removeEventListener('keydown', gameOverHandler);
                initTetrisGame();
            } else if (e.code === 'Escape') {
                document.removeEventListener('keydown', gameOverHandler);
                returnToGameList();
            }
        };

        document.addEventListener('keydown', gameOverHandler);
    }

    draw() {
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制背景
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 计算游戏区域位置
        const gameAreaWidth = this.boardWidth * this.blockSize;
        const gameAreaHeight = this.boardHeight * this.blockSize;
        const offsetX = (canvas.width - gameAreaWidth) / 2 - 100;
        const offsetY = (canvas.height - gameAreaHeight) / 2;

        // 绘制游戏区域边框
        ctx.strokeStyle = '#ecf0f1';
        ctx.lineWidth = 3;
        ctx.strokeRect(offsetX - 3, offsetY - 3, gameAreaWidth + 6, gameAreaHeight + 6);

        // 绘制游戏板
        this.drawBoard(offsetX, offsetY);

        // 绘制当前方块
        this.drawPiece(this.currentPiece, this.currentX, this.currentY, offsetX, offsetY);

        // 绘制UI
        this.drawUI(offsetX, offsetY, gameAreaWidth);

        // 如果游戏结束，显示结束画面
        if (!gameRunning) {
            this.showGameOverScreen();
        }
    }

    drawBoard(offsetX, offsetY) {
        for (let y = 0; y < this.boardHeight; y++) {
            for (let x = 0; x < this.boardWidth; x++) {
                const blockX = offsetX + x * this.blockSize;
                const blockY = offsetY + y * this.blockSize;

                if (this.board[y][x]) {
                    // 绘制已放置的方块
                    ctx.fillStyle = this.board[y][x];
                    ctx.fillRect(blockX, blockY, this.blockSize, this.blockSize);

                    // 绘制方块边框
                    ctx.strokeStyle = '#34495e';
                    ctx.lineWidth = 1;
                    ctx.strokeRect(blockX, blockY, this.blockSize, this.blockSize);
                } else {
                    // 绘制空格子
                    ctx.fillStyle = 'rgba(52, 73, 94, 0.1)';
                    ctx.fillRect(blockX, blockY, this.blockSize, this.blockSize);
                }
            }
        }
    }

    drawPiece(piece, x, y, offsetX, offsetY) {
        if (!piece) return;

        for (let py = 0; py < piece.shape.length; py++) {
            for (let px = 0; px < piece.shape[py].length; px++) {
                if (piece.shape[py][px]) {
                    const blockX = offsetX + (x + px) * this.blockSize;
                    const blockY = offsetY + (y + py) * this.blockSize;

                    // 只绘制在游戏区域内的部分
                    if (y + py >= 0) {
                        ctx.fillStyle = piece.color;
                        ctx.fillRect(blockX, blockY, this.blockSize, this.blockSize);

                        // 绘制高光效果
                        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                        ctx.fillRect(blockX, blockY, this.blockSize, 3);
                        ctx.fillRect(blockX, blockY, 3, this.blockSize);

                        // 绘制边框
                        ctx.strokeStyle = '#2c3e50';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(blockX, blockY, this.blockSize, this.blockSize);
                    }
                }
            }
        }
    }



    drawUI(offsetX, offsetY, gameAreaWidth) {
        const uiX = offsetX + gameAreaWidth + 30;

        // 绘制得分信息
        ctx.fillStyle = '#ecf0f1';
        ctx.font = 'bold 24px Arial';
        ctx.fillText('得分', uiX, offsetY + 30);
        ctx.font = '20px Arial';
        ctx.fillText(this.score.toString(), uiX, offsetY + 60);

        ctx.font = 'bold 24px Arial';
        ctx.fillText('行数', uiX, offsetY + 110);
        ctx.font = '20px Arial';
        ctx.fillText(this.lines.toString(), uiX, offsetY + 140);

        ctx.font = 'bold 24px Arial';
        ctx.fillText('等级', uiX, offsetY + 190);
        ctx.font = '20px Arial';
        ctx.fillText(this.level.toString(), uiX, offsetY + 220);

        // 绘制下一个方块
        ctx.font = 'bold 24px Arial';
        ctx.fillText('下一个', uiX, offsetY + 280);

        if (this.nextPiece) {
            const nextPieceX = uiX;
            const nextPieceY = offsetY + 300;

            // 绘制下一个方块的背景
            ctx.fillStyle = 'rgba(52, 73, 94, 0.3)';
            ctx.fillRect(nextPieceX, nextPieceY, 120, 80);
            ctx.strokeStyle = '#ecf0f1';
            ctx.lineWidth = 2;
            ctx.strokeRect(nextPieceX, nextPieceY, 120, 80);

            // 绘制下一个方块
            const blockSize = 20;
            const startX = nextPieceX + (120 - this.nextPiece.shape[0].length * blockSize) / 2;
            const startY = nextPieceY + (80 - this.nextPiece.shape.length * blockSize) / 2;

            for (let py = 0; py < this.nextPiece.shape.length; py++) {
                for (let px = 0; px < this.nextPiece.shape[py].length; px++) {
                    if (this.nextPiece.shape[py][px]) {
                        const blockX = startX + px * blockSize;
                        const blockY = startY + py * blockSize;

                        ctx.fillStyle = this.nextPiece.color;
                        ctx.fillRect(blockX, blockY, blockSize, blockSize);

                        ctx.strokeStyle = '#2c3e50';
                        ctx.lineWidth = 1;
                        ctx.strokeRect(blockX, blockY, blockSize, blockSize);
                    }
                }
            }
        }

        // 绘制操作说明
        ctx.font = '16px Arial';
        ctx.fillStyle = '#bdc3c7';
        const instructions = [
            '操作说明:',
            '← → 移动',
            '↓ 快速下落',
            '↑ 旋转',
            'Space 瞬间下降'
        ];

        instructions.forEach((instruction, index) => {
            ctx.fillText(instruction, uiX, offsetY + 420 + index * 25);
        });
    }

    gameLoop() {
        if (gameRunning || !gameRunning) { // 总是绘制
            const currentTime = Date.now();
            const deltaTime = currentTime - (this.lastTime || currentTime);
            this.lastTime = currentTime;

            if (gameRunning) {
                this.update(deltaTime);
            }

            this.draw();
            requestAnimationFrame(() => this.gameLoop());
        }
    }
}

// 初始化俄罗斯方块游戏
function initTetrisGame() {
    gameRunning = true;
    scoreElement.textContent = '0';

    // 清理之前的游戏实例
    if (tetrisGameInstance) {
        tetrisGameInstance.removeControls();
    }

    tetrisGameInstance = new TetrisGame();
    tetrisGameInstance.gameLoop();
}
