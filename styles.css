* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.game-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    padding: 20px 0;
}

.game-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.game-card.coming-soon {
    opacity: 0.6;
    cursor: default;
}

.game-card.coming-soon:hover {
    transform: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.game-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.game-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #333;
}

.game-card p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
}

.play-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover:not(:disabled) {
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
    transform: scale(1.05);
}

.play-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.game-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #2c3e50;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

.game-container.hidden {
    display: none;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 800px;
    margin-bottom: 20px;
    color: white;
}

.back-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
}

.back-btn:hover {
    background: #2980b9;
}

.score {
    font-size: 1.2rem;
    font-weight: bold;
}

#game-canvas {
    border: 3px solid #34495e;
    border-radius: 10px;
    background: #87CEEB;
    display: block;
}

.game-controls {
    margin-top: 20px;
    text-align: center;
    color: white;
}

.game-controls p {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.restart-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.restart-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .game-list {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    #game-canvas {
        width: 100%;
        max-width: 400px;
        height: auto;
    }
    
    .game-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
